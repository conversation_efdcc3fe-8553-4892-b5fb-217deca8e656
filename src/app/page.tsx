import Image from "next/image";

export default function Home() {
  return (
    <div className="min-h-screen bg-white dark:bg-slate-900">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/90 dark:bg-slate-900/90 backdrop-blur-sm z-50 border-b border-slate-200 dark:border-slate-700">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="text-xl font-semibold text-slate-800 dark:text-white">
              Neeharika Vemulapati
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#about" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">About</a>
              <a href="#experience" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Experience</a>
              <a href="#education" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Education</a>
              <a href="#skills" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Skills</a>
              <a href="#projects" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Projects</a>
              <a href="#contact" className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Contact</a>
            </div>
            <div className="md:hidden">
              <button className="text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 p-2">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-20 pb-16 px-4 sm:px-6 lg:px-8 bg-slate-50 dark:bg-slate-800">
        <div className="max-w-6xl mx-auto">
          <div className="text-center">
            <div className="mb-8">
              <div className="w-32 h-32 mx-auto mb-6 rounded-full overflow-hidden border-4 border-blue-500 shadow-lg">
                <img
                  src="/neeharika-photo.jpg"
                  alt="Neeharika Vemulapati"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-4 text-slate-900 dark:text-white">
              Hi, I'm <span className="text-blue-600 dark:text-blue-400">Neeharika</span>
            </h1>

            <p className="text-xl sm:text-2xl text-slate-600 dark:text-slate-300 mb-2">
              Senior Software Engineer
            </p>

            <p className="text-lg text-blue-600 dark:text-blue-400 mb-8">
              🏆 Microsoft AI Hackathon 2025 Winner
            </p>

            <p className="text-lg text-slate-600 dark:text-slate-300 mb-12 max-w-3xl mx-auto">
              Passionate about building innovative software solutions with expertise in
              Java, Python, AWS, and modern web technologies. Currently leading digital
              transformation initiatives at CSAA Insurance Group.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              <a
                href="#contact"
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-200"
              >
                Get In Touch
              </a>
              <a
                href="#projects"
                className="border border-blue-600 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-slate-700 px-8 py-3 rounded-lg font-medium transition-colors duration-200"
              >
                View My Work
              </a>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1">7+</div>
                <div className="text-sm text-slate-600 dark:text-slate-300">Years Experience</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1">7+</div>
                <div className="text-sm text-slate-600 dark:text-slate-300">Major Projects</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1">15+</div>
                <div className="text-sm text-slate-600 dark:text-slate-300">Technologies</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-900">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
              About Me
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              Senior Software Engineer with 7+ years of experience at Fortune 500 companies,
              specializing in enterprise applications, cloud architecture, and AI innovation.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12">
            <div>
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
                My Journey
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                My career spans from QA automation to senior engineering roles at AWS, American Express,
                FedEx, and CSAA Insurance Group. I've consistently earned recognition for technical
                excellence and leadership, including winning Microsoft's "Best Python Agent" award in 2025.
              </p>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Currently leading digital payments transformation at CSAA Insurance Group, I architect
                enterprise solutions using Java, Spring Boot, Python, and AWS. My expertise includes
                microservices, containerization, and modern CI/CD pipelines.
              </p>
              <p className="text-slate-600 dark:text-slate-300 mb-6">
                I'm passionate about AI innovation and contribute to healthcare technology through
                volunteer work at Phoenix Children's Hospital, building HIPAA-compliant solutions.
              </p>
            </div>

            <div className="space-y-6">
              <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg">
                <h4 className="font-semibold text-slate-900 dark:text-white mb-3">Current Focus</h4>
                <ul className="text-slate-600 dark:text-slate-300 space-y-2">
                  <li>• Digital Payments Transformation</li>
                  <li>• Enterprise Java & Spring Boot</li>
                  <li>• AWS Cloud Architecture</li>
                  <li>• AI Agents & Machine Learning</li>
                </ul>
              </div>

              <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg">
                <h4 className="font-semibold text-slate-900 dark:text-white mb-3">Key Achievements</h4>
                <ul className="text-slate-600 dark:text-slate-300 space-y-2">
                  <li>• Microsoft AI Hackathon Winner (2025)</li>
                  <li>• 2x SUN Award Winner (ASU)</li>
                  <li>• Agile Leadership Award (FedEx)</li>
                  <li>• 4.0 GPA Master's Degree</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Experience Section */}
      <section id="experience" className="py-16 px-4 sm:px-6 lg:px-8 bg-slate-50 dark:bg-slate-800">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
              Work Experience
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              My professional journey and key accomplishments.
            </p>
          </div>

          <div className="space-y-8">
            {/* Current Position */}
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                <div>
                  <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Senior Software Engineer</h3>
                  <p className="text-blue-600 dark:text-blue-400 font-medium">CSAA Insurance Group</p>
                  <p className="text-sm text-slate-500 dark:text-slate-400">Digital Payments Transformation</p>
                </div>
                <div className="text-slate-500 dark:text-slate-400 text-sm mt-2 md:mt-0">
                  <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs mr-2">Current</span>
                  Feb 2025 - Present
                </div>
              </div>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Leading digital payments transformation initiatives, focusing on modernizing payment systems and enhancing customer experience.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded text-sm">Java</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded text-sm">Spring Boot</span>
                <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded text-sm">AWS</span>
                <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded text-sm">Payments</span>
              </div>
            </div>

            {/* American Express */}
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                <div>
                  <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Software Development Engineer</h3>
                  <p className="text-blue-600 dark:text-blue-400 font-medium">American Express</p>
                </div>
                <div className="text-slate-500 dark:text-slate-400 text-sm mt-2 md:mt-0">
                  Jun 2024 - Jan 2025 · 8 mos
                </div>
              </div>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Developed enterprise payment solutions using Java and Spring Boot, working on critical financial systems.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded text-sm">Java</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded text-sm">Spring Boot</span>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded text-sm">REST APIs</span>
                <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded text-sm">AWS</span>
              </div>
            </div>

            {/* AWS */}
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                <div>
                  <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Software Development Engineer</h3>
                  <p className="text-blue-600 dark:text-blue-400 font-medium">Amazon Web Services (AWS)</p>
                  <p className="text-sm text-slate-500 dark:text-slate-400">Elastic Block Storage Team</p>
                </div>
                <div className="text-slate-500 dark:text-slate-400 text-sm mt-2 md:mt-0">
                  Sep 2022 - Mar 2023 · 7 mos
                </div>
              </div>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Enhanced system reliability and reduced deployment times by 40% using Terraform and CloudWatch.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded text-sm">Java</span>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded text-sm">Python</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded text-sm">Terraform</span>
                <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded text-sm">AWS</span>
              </div>
            </div>

            {/* FedEx */}
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                <div>
                  <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Technical Team Lead</h3>
                  <p className="text-blue-600 dark:text-blue-400 font-medium">FedEx Corporation</p>
                  <p className="text-sm text-slate-500 dark:text-slate-400">🏆 Agile Leadership Award Winner</p>
                </div>
                <div className="text-slate-500 dark:text-slate-400 text-sm mt-2 md:mt-0">
                  Oct 2019 - Oct 2020 · 1 yr 1 mo
                </div>
              </div>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Led high-performing team, elevated software quality by 30%, and achieved 100% code coverage.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-3 py-1 rounded text-sm">Java</span>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded text-sm">Python</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded text-sm">Jenkins</span>
                <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-3 py-1 rounded text-sm">Team Leadership</span>
              </div>
            </div>

          </div>
        </div>
      </section>

      {/* Education Section */}
      <section id="education" className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-900">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
              Education
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              My academic background and achievements.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Master's Degree */}
            <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg shadow-md">
              <div className="mb-4">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white">Master's in Information Technology</h3>
                <p className="text-blue-600 dark:text-blue-400 font-medium">Arizona State University</p>
                <span className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded text-xs font-medium">4.0 GPA</span>
              </div>
              <p className="text-slate-600 dark:text-slate-300 text-sm mb-4">
                Advanced studies in Information Technology with perfect academic performance.
                Specialized in enterprise systems, cloud computing, and software architecture.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">Enterprise Systems</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">Cloud Computing</span>
                <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-xs">Software Architecture</span>
              </div>
            </div>

            {/* Bachelor's Degree */}
            <div className="bg-slate-50 dark:bg-slate-800 p-6 rounded-lg shadow-md">
              <div className="mb-4">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white">BTech in Computer Engineering</h3>
                <p className="text-blue-600 dark:text-blue-400 font-medium">IIITDM Kancheepuram</p>
                <p className="text-xs text-slate-500 dark:text-slate-400">Indian Institute of Information Technology</p>
              </div>
              <p className="text-slate-600 dark:text-slate-300 text-sm mb-4">
                Comprehensive engineering education in Computer Engineering from a prestigious IIIT.
                Strong foundation in computer science fundamentals and programming.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">Computer Engineering</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-xs">Programming</span>
                <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-xs">Engineering</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Skills Section */}
      <section id="skills" className="py-16 px-4 sm:px-6 lg:px-8 bg-slate-50 dark:bg-slate-800">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
              Skills & Technologies
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              Technologies and tools I work with to build innovative solutions.
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-6">
            {/* Programming Languages */}
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">Programming</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Java</span>
                  <span className="text-blue-600 dark:text-blue-400 font-medium">Expert</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Python</span>
                  <span className="text-blue-600 dark:text-blue-400 font-medium">Expert</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">JavaScript</span>
                  <span className="text-blue-600 dark:text-blue-400 font-medium">Advanced</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">C#</span>
                  <span className="text-blue-600 dark:text-blue-400 font-medium">Intermediate</span>
                </div>
              </div>
            </div>

            {/* Frameworks & Libraries */}
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">Frameworks</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Spring Boot</span>
                  <span className="text-green-600 dark:text-green-400 font-medium">Expert</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">React.js</span>
                  <span className="text-green-600 dark:text-green-400 font-medium">Advanced</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Node.js</span>
                  <span className="text-green-600 dark:text-green-400 font-medium">Advanced</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Apache Kafka</span>
                  <span className="text-green-600 dark:text-green-400 font-medium">Advanced</span>
                </div>
              </div>
            </div>

            {/* Cloud & DevOps */}
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">Cloud & DevOps</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">AWS</span>
                  <span className="text-purple-600 dark:text-purple-400 font-medium">Expert</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Docker</span>
                  <span className="text-purple-600 dark:text-purple-400 font-medium">Advanced</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Kubernetes</span>
                  <span className="text-purple-600 dark:text-purple-400 font-medium">Advanced</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Jenkins</span>
                  <span className="text-purple-600 dark:text-purple-400 font-medium">Advanced</span>
                </div>
              </div>
            </div>

            {/* Testing & QA */}
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">Testing & QA</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Selenium</span>
                  <span className="text-orange-600 dark:text-orange-400 font-medium">Expert</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Cypress.io</span>
                  <span className="text-orange-600 dark:text-orange-400 font-medium">Advanced</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">Appium</span>
                  <span className="text-orange-600 dark:text-orange-400 font-medium">Advanced</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600 dark:text-slate-300">BrowserStack</span>
                  <span className="text-orange-600 dark:text-orange-400 font-medium">Intermediate</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-900">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
              Featured Projects
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              Some of the projects I've worked on that showcase my skills and experience.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">

            {/* VCT Hackathon */}
            <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-md">
              <div className="mb-4">
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs font-medium">🏆 Award Winner</span>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3">
                VCT Hackathon Project
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                AI-powered digital assistant for VALORANT esports team optimization using vector databases and agent-based architecture.
              </p>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-sm">Python</span>
                <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-2 py-1 rounded text-sm">AWS</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-sm">RAG</span>
                <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">LLM</span>
              </div>
              <a href="https://github.com/neeharve/VCT_Hackathon" className="text-blue-600 dark:text-blue-400 hover:underline">
                View on GitHub
              </a>
            </div>

            {/* Cloud Inventory Management */}
            <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3">
                Cloud Inventory Management
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Enterprise inventory system with ML-powered demand forecasting, built on AWS with React frontend.
              </p>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-2 py-1 rounded text-sm">AWS</span>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-sm">Java</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-sm">React</span>
                <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">ML</span>
              </div>
              <span className="text-slate-500 dark:text-slate-400 text-sm italic">Enterprise Project</span>
            </div>

            {/* Cruddr */}
            <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3">
                Cruddr - Micro-blogging Platform
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Production-ready micro-blogging platform with 25% cost reduction and 30% performance improvement.
              </p>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-sm">React</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-sm">Flask</span>
                <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-2 py-1 rounded text-sm">AWS</span>
                <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">Docker</span>
              </div>
              <span className="text-slate-500 dark:text-slate-400 text-sm italic">Production Application</span>
            </div>

            {/* Healthcare Data Pipeline */}
            <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3">
                Healthcare Data Pipeline
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Automated data processing pipeline for healthcare analytics with Azure and Python.
              </p>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-sm">Python</span>
                <span className="bg-cyan-100 dark:bg-cyan-900 text-cyan-800 dark:text-cyan-200 px-2 py-1 rounded text-sm">Azure</span>
                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-sm">Databricks</span>
              </div>
              <span className="text-slate-500 dark:text-slate-400 text-sm italic">Healthcare Analytics</span>
            </div>

            {/* AR Tourism */}
            <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-md">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3">
                AR Tourism App
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-4">
                Augmented reality application for tourism with interactive map views and points of interest.
              </p>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-2 py-1 rounded text-sm">Swift</span>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-sm">Dart</span>
                <span className="bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200 px-2 py-1 rounded text-sm">AR</span>
              </div>
              <span className="text-slate-500 dark:text-slate-400 text-sm italic">ASU Academic Project</span>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-16 px-4 sm:px-6 lg:px-8 bg-slate-50 dark:bg-slate-800">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
              Get In Touch
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              I'm always open to discussing new opportunities, interesting projects, or just having a chat about technology.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <div className="mb-4">
                <svg className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
              </div>
              <h3 className="font-semibold text-slate-900 dark:text-white mb-2">Email</h3>
              <p className="text-slate-600 dark:text-slate-300 text-sm"><EMAIL></p>
              <p className="text-slate-600 dark:text-slate-300 text-sm"><EMAIL></p>
            </div>

            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <div className="mb-4">
                <svg className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="font-semibold text-slate-900 dark:text-white mb-2">LinkedIn</h3>
              <a href="https://www.linkedin.com/in/neeharika-vemulapati" className="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                linkedin.com/in/neeharika-vemulapati
              </a>
            </div>

            <div className="bg-white dark:bg-slate-900 p-6 rounded-lg shadow-md">
              <div className="mb-4">
                <svg className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="font-semibold text-slate-900 dark:text-white mb-2">GitHub</h3>
              <a href="https://github.com/neeharve" className="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                github.com/neeharve
              </a>
            </div>
          </div>

        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 dark:bg-black text-white py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto text-center">
          <p className="text-slate-300 mb-2">
            © 2025 Neeharika Vemulapati. All rights reserved.
          </p>
          <p className="text-slate-400 text-sm">
            Built with Next.js and Tailwind CSS
          </p>
        </div>
      </footer>
    </div>
  );
}
